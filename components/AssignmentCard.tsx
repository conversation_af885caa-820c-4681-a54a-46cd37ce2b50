import React, { useState } from 'react';
import { Assignment } from '../types';
import { SUBJECT_COLORS } from '../constants';
import { CheckIcon, LightningIcon } from './icons';

interface AssignmentCardProps {
  assignment: Assignment;
  onToggleComplete: (id: string) => void;
}

const AssignmentCard: React.FC<AssignmentCardProps> = ({ assignment, onToggleComplete }) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const isCompleted = !!assignment.completionDate;
  const colors = SUBJECT_COLORS[assignment.subject];
  const subjectColor = `${colors.base} ${colors.dark}`;
  
  const handleToggle = () => {
    if (!isCompleted) {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 500); // Animation duration
    }
    onToggleComplete(assignment.id);
  }

  return (
    <div
      className={`
        relative flex items-start gap-4 p-4 rounded-lg transition-all duration-300
        bg-slate-100/80 dark:bg-slate-900/70 border border-slate-300/70 dark:border-slate-800/70
        overflow-hidden
        ${isCompleted ? 'opacity-60' : 'hover:bg-white/50 dark:hover:bg-slate-800/90'}
      `}
    >
      <div className={`absolute left-0 top-0 bottom-0 w-1.5 bg-current ${colors.base.split(' ')[0]} dark:bg-opacity-60 shadow-lg ${colors.neon}`}></div>

      <button
        onClick={handleToggle}
        aria-label={`Mark ${assignment.name} as ${isCompleted ? 'incomplete' : 'complete'}`}
        className={`
          flex-shrink-0 w-7 h-7 rounded-md border-2 transition-all duration-200
          flex items-center justify-center mt-0.5
          ${isCompleted 
            ? 'bg-green-500 border-green-500' 
            : 'border-slate-400 dark:border-slate-600 bg-white dark:bg-slate-900 hover:border-green-400 dark:hover:border-green-500'}
        `}
      >
        {isCompleted && <CheckIcon className="w-5 h-5 text-white" />}
      </button>

      <div className="flex-grow">
        <p
          className={`text-slate-800 dark:text-slate-200 font-bold text-lg leading-tight ${isCompleted ? 'line-through' : ''}`}
        >
          {assignment.name}
        </p>
        <div className="flex items-center gap-4 mt-1">
          <span
            className={`text-xs font-bold px-2.5 py-1 rounded-full ${subjectColor}`}
          >
            {assignment.subject}
          </span>
          <span className="flex items-center gap-1 text-sm font-bold text-yellow-600 dark:text-yellow-400">
            <LightningIcon className="w-4 h-4" />
            +{assignment.xp} J
          </span>
        </div>
      </div>
      {isAnimating && <div className="absolute inset-0 bg-yellow-400/50 rounded-lg animate-ping"></div>}
    </div>
  );
};

export default AssignmentCard;