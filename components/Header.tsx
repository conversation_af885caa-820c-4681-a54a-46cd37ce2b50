import React from 'react';
import { SunIcon, MoonIcon, BookOpenIcon, FlameIcon, TornadoIcon, CalendarIcon } from './icons';
import XPBar from './XPBar';

interface HeaderProps {
  isDarkMode: boolean;
  onToggleDarkMode: () => void;
  onViewSubjects: () => void;
  level: number;
  totalXp: number;
  xpForNextLevel: number;
  xpForCurrentLevel: number;
  dailyStreak: number;
  completedCount: number;
  totalCount: number;
  estimatedFinishDate: Date;
}

const Header: React.FC<HeaderProps> = ({ 
  isDarkMode,
  onToggleDarkMode,
  onViewSubjects,
  level,
  totalXp,
  xpForNextLevel,
  xpForCurrentLevel,
  dailyStreak,
  completedCount,
  totalCount,
  estimatedFinishDate,
}) => {
  const xpProgress = xpForNextLevel > xpForCurrentLevel ? (totalXp - xpForCurrentLevel) / (xpForNextLevel - xpForCurrentLevel) * 100 : 100;

  const formatDate = (d: Date) => {
    return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  return (
    <header className="bg-slate-100/80 dark:bg-slate-950/80 backdrop-blur-lg sticky top-0 z-20 border-b border-slate-300/70 dark:border-slate-800/70">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-sky-600 dark:text-sky-400 tracking-wider font-orbitron uppercase">
              7th Grade Storm Quest
            </h1>
            <p className="text-slate-500 dark:text-slate-400 font-semibold">Track the forecast, chase the grade.</p>
          </div>
          <div className="flex items-center space-x-2 sm:space-x-3">
            <button
              onClick={onViewSubjects}
              className="p-2 rounded-full text-slate-500 dark:text-slate-400 hover:bg-slate-200/70 dark:hover:bg-slate-800/70"
              aria-label="View all subjects"
            >
              <BookOpenIcon className="w-6 h-6" />
            </button>
            <button 
              onClick={onToggleDarkMode}
              className="p-2 rounded-full text-slate-500 dark:text-slate-400 hover:bg-slate-200/70 dark:hover:bg-slate-800/70"
              aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
            >
              {isDarkMode ? <SunIcon className="w-6 h-6 text-yellow-400" /> : <MoonIcon className="w-6 h-6" />}
            </button>
          </div>
        </div>

        <div className="bg-slate-200/50 dark:bg-slate-900/50 rounded-lg p-4 border border-slate-300/70 dark:border-slate-800/70">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <div className="flex-shrink-0 text-center">
                    <div className="relative w-20 h-20 mx-auto group">
                        <TornadoIcon className="w-full h-full text-slate-500 dark:text-slate-400 group-hover:text-sky-500 transition-colors duration-300" />
                        <div className="absolute inset-0 flex flex-col items-center justify-center">
                            <span className="text-xs text-slate-500 dark:text-slate-400 font-semibold">CAT</span>
                            <span className="font-orbitron font-bold text-3xl text-slate-800 dark:text-white">{level}</span>
                        </div>
                    </div>
                </div>

                <div className="flex-grow">
                    <XPBar 
                        progress={xpProgress} 
                        currentXp={totalXp - xpForCurrentLevel}
                        xpToNextLevel={xpForNextLevel - xpForCurrentLevel}
                    />
                    <div className="mt-2 grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 text-center">
                        <StatCard label="Total Energy" value={`${totalXp.toLocaleString()} J`} />
                        <StatCard label="Quests Done" value={`${completedCount}/${totalCount}`} />
                        <StatCard label="Storm Surge" value={dailyStreak} icon={<FlameIcon className={`w-4 h-4 ${dailyStreak > 0 ? 'text-orange-500' : 'text-slate-400'}`} />} />
                        <StatCard label="ETA" value={formatDate(estimatedFinishDate)} icon={<CalendarIcon className="w-4 h-4 text-slate-500 dark:text-slate-400" />} />
                    </div>
                </div>
            </div>
        </div>

      </div>
    </header>
  );
};

const StatCard: React.FC<{label:string, value: string | number, icon?: React.ReactNode}> = ({label, value, icon}) => (
    <div className="bg-slate-100 dark:bg-slate-800/70 p-2 rounded-md border border-slate-300/70 dark:border-slate-700/70">
        <p className="text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider">{label}</p>
        <div className="flex items-center justify-center gap-1">
            {icon}
            <p className="text-lg font-bold font-orbitron text-slate-700 dark:text-slate-200">{value}</p>
        </div>
    </div>
)

export default Header;