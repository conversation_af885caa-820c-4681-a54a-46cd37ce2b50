import React from 'react';

interface XPBarProps {
  progress: number;
  currentXp: number;
  xpToNextLevel: number;
}

const XPBar: React.FC<XPBarProps> = ({ progress, currentXp, xpToNextLevel }) => {
  return (
    <div className="w-full">
      <div className="flex justify-between items-end mb-1 text-sm">
        <span className="font-semibold text-slate-600 dark:text-slate-300">Level Progress</span>
        <span className="font-bold text-slate-700 dark:text-slate-200 font-orbitron">
          {currentXp.toLocaleString()} / {xpToNextLevel.toLocaleString()} XP
        </span>
      </div>
      <div className="w-full bg-slate-300 dark:bg-slate-700 rounded-full h-4 border-2 border-slate-400/50 dark:border-slate-600/50 p-0.5">
        <div
          className="bg-gradient-to-r from-cyan-400 to-blue-500 h-full rounded-full transition-all duration-500 ease-out"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    </div>
  );
};

export default XPBar;
