@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations and styles for Storm Quest */
@keyframes wiggle {
  0%, 100% { transform: rotate(-3deg); }
  50% { transform: rotate(3deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(56, 189, 248, 0.5); }
  50% { box-shadow: 0 0 20px rgba(56, 189, 248, 0.8), 0 0 30px rgba(56, 189, 248, 0.6); }
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Storm-themed gradients */
.bg-storm-gradient {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
}

.bg-storm-gradient-dark {
  background: linear-gradient(135deg, #075985 0%, #1e40af 50%, #3730a3 100%);
}

/* Achievement badge styles */
.achievement-badge {
  @apply relative overflow-hidden rounded-full p-1;
  background: conic-gradient(from 0deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
}

.achievement-badge::before {
  content: '';
  position: absolute;
  inset: 2px;
  background: inherit;
  border-radius: inherit;
  background: #1f2937;
}

/* XP bar enhancements */
.xp-bar-glow {
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

/* Subject color themes */
.subject-science { @apply bg-teal-100 dark:bg-teal-900/30 border-teal-300 dark:border-teal-700; }
.subject-spanish { @apply bg-rose-100 dark:bg-rose-900/30 border-rose-300 dark:border-rose-700; }
.subject-journalism { @apply bg-amber-100 dark:bg-amber-900/30 border-amber-300 dark:border-amber-700; }
.subject-civics { @apply bg-lime-100 dark:bg-lime-900/30 border-lime-300 dark:border-lime-700; }
.subject-language { @apply bg-violet-100 dark:bg-violet-900/30 border-violet-300 dark:border-violet-700; }
.subject-math { @apply bg-sky-100 dark:bg-sky-900/30 border-sky-300 dark:border-sky-700; }

/* Modern hover effects for interactive elements */
.hover-lift {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Modern glass morphism effect */
.glass-effect {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Enhanced focus states for accessibility */
button:focus-visible,
input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Subtle animations for better perceived performance */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Storm weather background patterns - removed for better readability */

/* Completion celebration styles */
.celebration-text {
  background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
  background-size: 400% 400%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}
