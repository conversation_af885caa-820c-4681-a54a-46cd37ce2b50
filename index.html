<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>7th Grade Storm Quest</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@700;900&family=Rajdhani:wght@500;700&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Rajdhani', sans-serif;
        background-image: 
          radial-gradient(circle at 25px 25px, rgba(13, 148, 136, 0.1) 2%, transparent 0%),
          radial-gradient(circle at 75px 75px, rgba(59, 130, 246, 0.05) 2%, transparent 0%);
        background-size: 100px 100px;
        background-position: 0 0, 50px 50px;
      }
      .dark body {
        background-image: 
          radial-gradient(circle at 15px 15px, rgba(56, 189, 248, 0.1) 1%, transparent 0%),
          radial-gradient(circle at 65px 65px, rgba(99, 102, 241, 0.08) 1%, transparent 0%);
        background-size: 150px 150px;
        background-position: 0 0, 75px 75px;
      }
      .font-orbitron {
        font-family: 'Orbitron', sans-serif;
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-slate-50 dark:bg-slate-950 transition-colors duration-300">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>